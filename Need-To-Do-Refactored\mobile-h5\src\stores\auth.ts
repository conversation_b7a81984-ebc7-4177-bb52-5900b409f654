import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, ApiResponse } from '@/types'
import { STORAGE_KEYS } from '@/constants'

// 简化版API，避免循环依赖
const mockApi = {
  defaults: { headers: { common: {} as Record<string, string> } },
  async post(url: string, data?: any) {
    console.log('Mock API POST:', url, data)
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (url === '/auth/student-login') {
      return {
        data: {
          success: true,
          data: {
            token: 'mock_token_' + Date.now(),
            user: {
              id: 1,
              studentId: data.studentId,
              realName: '张三',
              nickname: '张三',
              role: 'student',
              language: data.language
            }
          }
        }
      }
    }
    return { data: { success: true } }
  },
  async get() { return { data: { success: true, data: {} } } },
  async put() { return { data: { success: true } } }
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>('')
  const user = ref<User | null>(null)
  const isLoading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const userRole = computed(() => user.value?.role || '')
  const userName = computed(() => user.value?.realName || user.value?.nickname || '')

  // 初始化认证状态
  const initAuth = () => {
    try {
      const savedToken = localStorage.getItem(STORAGE_KEYS.TOKEN)
      const savedUser = localStorage.getItem(STORAGE_KEYS.USER)
      
      if (savedToken && savedUser) {
        token.value = savedToken
        user.value = JSON.parse(savedUser)
        
        // 设置API默认请求头
        mockApi.defaults.headers.common['Authorization'] = `Bearer ${savedToken}`
      }
    } catch (error) {
      console.error('初始化认证状态失败:', error)
      clearAuth()
    }
  }

  // 学生登录
  const studentLogin = async (studentId: string, language = 'zh-cn') => {
    isLoading.value = true
    
    try {
      const response = await mockApi.post('/auth/student-login', {
        studentId,
        language
      })

      if (response.data.success && response.data.data) {
        const { token: newToken, user: userData } = response.data.data
        
        // 保存认证信息
        token.value = newToken
        user.value = userData
        
        // 持久化存储
        localStorage.setItem(STORAGE_KEYS.TOKEN, newToken)
        localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(userData))
        localStorage.setItem(STORAGE_KEYS.LANGUAGE, language)
        
        // 设置API默认请求头
        mockApi.defaults.headers.common['Authorization'] = `Bearer ${newToken}`
        
        return userData
      } else {
        throw new Error(response.data.message || '登录失败')
      }
    } catch (error: any) {
      console.error('学生登录失败:', error)
      
      // 处理不同类型的错误
      if (error.response) {
        const { status, data } = error.response
        
        switch (status) {
          case 400:
            throw new Error(data.message || '请求参数错误')
          case 401:
            throw new Error('学号不存在或无效')
          case 403:
            throw new Error('账号已被禁用')
          case 500:
            throw new Error('服务器错误，请稍后重试')
          default:
            throw new Error(data.message || '登录失败')
        }
      } else if (error.request) {
        throw new Error('网络连接失败，请检查网络后重试')
      } else {
        throw new Error(error.message || '登录失败')
      }
    } finally {
      isLoading.value = false
    }
  }

  // 刷新Token (简化版)
  const refreshToken = async () => {
    console.log('刷新Token (模拟)')
    return token.value
  }

  // 获取用户信息 (简化版)
  const fetchUserInfo = async () => {
    console.log('获取用户信息 (模拟)')
    return user.value
  }

  // 更新用户信息 (简化版)
  const updateUserInfo = async (updates: Partial<User>) => {
    console.log('更新用户信息 (模拟)', updates)
    if (user.value) {
      user.value = { ...user.value, ...updates }
      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user.value))
    }
    return user.value
  }

  // 退出登录
  const logout = async () => {
    try {
      // 调用后端登出接口 (模拟)
      await mockApi.post('/auth/logout')
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      clearAuth()
    }
  }

  // 清除认证状态
  const clearAuth = () => {
    token.value = ''
    user.value = null
    
    // 清除本地存储
    localStorage.removeItem(STORAGE_KEYS.TOKEN)
    localStorage.removeItem(STORAGE_KEYS.USER)
    
    // 清除API请求头
    delete mockApi.defaults.headers.common['Authorization']
  }

  // 检查Token是否有效
  const checkTokenValidity = async () => {
    if (!token.value) {
      return false
    }

    try {
      await fetchUserInfo()
      return true
    } catch (error) {
      console.error('Token验证失败:', error)
      clearAuth()
      return false
    }
  }

  // 自动刷新Token
  const setupTokenRefresh = () => {
    // 每20分钟检查一次Token状态
    setInterval(async () => {
      if (isLoggedIn.value) {
        try {
          await refreshToken()
        } catch (error) {
          console.error('自动刷新Token失败:', error)
        }
      }
    }, 20 * 60 * 1000) // 20分钟
  }

  // 处理Token过期
  const handleTokenExpired = () => {
    clearAuth()
    // 可以在这里添加跳转到登录页的逻辑
    window.location.href = '/login'
  }

  return {
    // 状态
    token,
    user,
    isLoading,
    
    // 计算属性
    isLoggedIn,
    userRole,
    userName,
    
    // 方法
    initAuth,
    studentLogin,
    refreshToken,
    fetchUserInfo,
    updateUserInfo,
    logout,
    clearAuth,
    checkTokenValidity,
    setupTokenRefresh,
    handleTokenExpired
  }
})
